// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            Int       @id @default(autoincrement())
  name          String
  email         String    @unique
  password      String
  role          String    @default("user") // admin, user
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations for data ownership
  chitFunds     ChitFund[]
  globalMembers GlobalMember[]
  loans         Loan[]
}

model ChitFund {
  id                  Int           @id @default(autoincrement())
  name                String
  totalAmount         Float
  monthlyContribution Float
  duration            Int           // in months
  membersCount        Int
  status              String        @default("Active") // Active, Completed, Upcoming
  startDate           DateTime
  currentMonth        Int           @default(1)
  nextAuctionDate     DateTime?
  description         String?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Data ownership
  createdById         Int
  createdBy           User          @relation(fields: [createdById], references: [id])

  // Relations
  members             Member[]
  contributions       Contribution[]
  auctions            Auction[]
}

model GlobalMember {
  id              Int           @id @default(autoincrement())
  name            String
  contact         String
  email           String?
  address         String?
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Data ownership
  createdById     Int
  createdBy       User          @relation(fields: [createdById], references: [id])

  // Relations
  chitFundMembers Member[]
  loans           Loan[]        @relation("BorrowerToLoan")
}

model Member {
  id              Int           @id @default(autoincrement())
  globalMemberId  Int
  chitFundId      Int
  joinDate        DateTime      @default(now())
  auctionWon      Boolean       @default(false)
  auctionMonth    Int?
  contribution    Float
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  globalMember    GlobalMember  @relation(fields: [globalMemberId], references: [id])
  chitFund        ChitFund      @relation(fields: [chitFundId], references: [id])
  contributions   Contribution[]
  auctions        Auction[]
}

model Contribution {
  id                     Int           @id @default(autoincrement())
  amount                 Float
  month                  Int
  paidDate               DateTime
  memberId               Int
  chitFundId             Int
  balance                Float         @default(0)  // Track remaining balance if partial payment
  balancePaymentDate     DateTime?     // Expected date when the balance will be paid
  balancePaymentStatus   String?       @default("Pending") // Status of balance payment: "Pending", "Paid", "Overdue"
  actualBalancePaymentDate DateTime?   // Actual date when the balance was paid
  notes                  String?       // Additional notes about the contribution
  createdAt              DateTime      @default(now())
  updatedAt              DateTime      @updatedAt
  member                 Member        @relation(fields: [memberId], references: [id])
  chitFund               ChitFund      @relation(fields: [chitFundId], references: [id])
}

model Auction {
  id              Int           @id @default(autoincrement())
  chitFundId      Int
  month           Int
  date            DateTime
  winnerId        Int
  amount          Float
  lowestBid       Float?        // Lowest bid amount in the auction
  highestBid      Float?        // Highest bid amount in the auction
  numberOfBidders Int?          // Number of members who participated in bidding
  notes           String?       // Additional notes about the auction
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  chitFund        ChitFund      @relation(fields: [chitFundId], references: [id])
  winner          Member        @relation(fields: [winnerId], references: [id])
}

model Loan {
  id                Int           @id @default(autoincrement())
  borrowerId        Int
  loanType          String        // Business, Personal, Education, etc.
  amount            Float
  interestRate      Float
  documentCharge    Float         @default(0)  // Document processing charge
  currentMonth      Int           @default(0)  // Current month of the loan (0 for future loans, 1+ for active loans)
  installmentAmount   Float      @default(0)  // Payment installment amount
  duration          Int           // in months
  disbursementDate  DateTime
  repaymentType     String        // Monthly, Weekly, etc.
  remainingAmount   Float
  overdueAmount     Float         @default(0)  // Amount that is overdue
  missedPayments    Int           @default(0)  // Number of missed payments
  nextPaymentDate   DateTime?
  status            String        @default("Active") // Active, Completed, Defaulted
  purpose           String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Data ownership
  createdById       Int
  createdBy         User          @relation(fields: [createdById], references: [id])

  // Relations
  borrower          GlobalMember  @relation("BorrowerToLoan", fields: [borrowerId], references: [id])
  repayments        Repayment[]
  paymentSchedules  PaymentSchedule[]
}

model Repayment {
  id              Int           @id @default(autoincrement())
  amount          Float
  paidDate        DateTime
  loanId          Int
  paymentType     String        @default("full") // "full" or "interestOnly"
  period          Int           // Month or week number in the loan term (required)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  loan            Loan          @relation(fields: [loanId], references: [id])
}

model PaymentSchedule {
  id                Int           @id @default(autoincrement())
  loanId            Int
  period            Int           // Month or week number in the loan term
  dueDate           DateTime
  amount            Float
  status            String        @default("Pending") // Pending, Paid, Missed, InterestOnly
  actualPaymentDate DateTime?     // Date when payment was actually made
  notes             String?       // Additional notes about the payment
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  loan              Loan          @relation(fields: [loanId], references: [id])
}

